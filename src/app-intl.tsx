import { useState, type PropsWithChildren } from "react";
import { IntlProvider } from "react-intl";


import { AppLocale } from "../../core.constants";

export type AppIntlProps = {
  enMessages: Record<string, string>;
  ruMessages: Record<string, string>;
  uaMessages: Record<string, string>;
};

export const AppIntl = ({
  enMessages,
  ruMessages,
  uaMessages,
  children,
}: PropsWithChildren<AppIntlProps>) => {
  const [locale, setLocale] = useState(AppLocale.ua);

  const messages = {
    [AppLocale.en]: enMessages,
    [AppLocale.ru]: ruMessages,
    [AppLocale.uk]: uaMessages,
  };

  return (
    <IntlProvider
      {...{
        locale: locale,
        messages: messages[locale],
      }}
    >
      {children}
    </IntlProvider>
  );
};
