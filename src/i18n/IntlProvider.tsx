"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { IntlProvider as ReactIntlProvider } from 'react-intl';
import { Locale, DEFAULT_LOCALE, messages, getBrowserLocale, LOCALE_STORAGE_KEY } from './index';

interface IntlContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  toggleLocale: () => void;
}

const IntlContext = createContext<IntlContextType | undefined>(undefined);

interface IntlProviderProps {
  children: ReactNode;
}

export const IntlProvider: React.FC<IntlProviderProps> = ({ children }) => {
  const [locale, setLocaleState] = useState<Locale>(DEFAULT_LOCALE);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    // Get locale from localStorage or browser
    const savedLocale = localStorage.getItem(LOCALE_STORAGE_KEY) as Locale;
    const initialLocale = savedLocale || getBrowserLocale();
    setLocaleState(initialLocale);
  }, []);

  const setLocale = (newLocale: Locale) => {
    setLocaleState(newLocale);
    if (isClient) {
      localStorage.setItem(LOCALE_STORAGE_KEY, newLocale);
    }
  };

  const toggleLocale = () => {
    const newLocale = locale === 'en' ? 'ua' : 'en';
    setLocale(newLocale);
  };

  const contextValue: IntlContextType = {
    locale,
    setLocale,
    toggleLocale,
  };

  // Don't render until client-side to avoid hydration mismatch
  if (!isClient) {
    return (
      <ReactIntlProvider locale={DEFAULT_LOCALE} messages={messages[DEFAULT_LOCALE]}>
        <IntlContext.Provider value={contextValue}>
          {children}
        </IntlContext.Provider>
      </ReactIntlProvider>
    );
  }

  return (
    <ReactIntlProvider locale={locale} messages={messages[locale]}>
      <IntlContext.Provider value={contextValue}>
        {children}
      </IntlContext.Provider>
    </ReactIntlProvider>
  );
};

export const useIntl = () => {
  const context = useContext(IntlContext);
  if (context === undefined) {
    throw new Error('useIntl must be used within an IntlProvider');
  }
  return context;
};
