import { createIntl, createIntlCache } from "react-intl";

// Import message files
import enMessages from "./locales/en.json";
import uaMessages from "./locales/ua.json";

// Supported locales
export const LOCALES = {
  EN: "en",
  UA: "ua",
} as const;

export type Locale = (typeof LOCALES)[keyof typeof LOCALES];

// Default locale
export const DEFAULT_LOCALE: Locale = LOCALES.EN;

// Messages object
export const messages = {
  [LOCALES.EN]: enMessages,
  [LOCALES.UA]: uaMessages,
};

// Create intl cache
const cache = createIntlCache();

// Create intl instance
export const createIntlInstance = (locale: Locale = DEFAULT_LOCALE) => {
  return createIntl(
    {
      locale,
      messages: messages[locale],
    },
    cache
  );
};

// Get browser locale
export const getBrowserLocale = (): Locale => {
  if (typeof window === "undefined") return DEFAULT_LOCALE;

  const browserLang = navigator.language.split("-")[0];
  return Object.values(LOCALES).includes(browserLang as Locale)
    ? (browserLang as Locale)
    : DEFAULT_LOCALE;
};

// Locale storage key
export const LOCALE_STORAGE_KEY = "ivan-boxing-locale";
