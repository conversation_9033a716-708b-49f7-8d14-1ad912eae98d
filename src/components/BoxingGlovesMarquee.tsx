"use client";

import { JSX, useEffect, useId, useRef, useState } from "react";
import { motion, useAnimation, useInView } from "framer-motion";
import { cn } from "@/lib/utils";

interface MarqueeProps {
  className?: string;
  reverse?: boolean;
  pauseOnHover?: boolean;
  children?: React.ReactNode;
  vertical?: boolean;
  repeat?: number;
  [key: string]: unknown;
}

function Marquee({
  className,
  reverse,
  pauseOnHover = false,
  children,
  vertical = false,
  repeat = 4,
  ...props
}: MarqueeProps) {
  return (
    <div
      {...props}
      className={cn(
        "group flex overflow-hidden p-2 [--duration:40s] [--gap:1rem] [gap:var(--gap)]",
        {
          "flex-row": !vertical,
          "flex-col": vertical,
        },
        className
      )}
    >
      {Array(repeat)
        .fill(0)
        .map((_, i) => (
          <div
            key={i}
            className={cn("flex shrink-0 justify-around [gap:var(--gap)]", {
              "animate-marquee flex-row": !vertical,
              "animate-marquee-vertical flex-col": vertical,
              "group-hover:[animation-play-state:paused]": pauseOnHover,
              "[animation-direction:reverse]": reverse,
            })}
          >
            {children}
          </div>
        ))}
    </div>
  );
}

// Boxing gloves in different colors and styles
const boxingGloves = [
  {
    icon: (
      <svg
        viewBox="0 0 100 100"
        className="size-full"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M25 30C25 25 30 20 35 20H45C50 20 55 25 55 30V50C55 55 50 60 45 60H35C30 60 25 55 25 50V30Z"
          fill="#dc2626"
          stroke="#991b1b"
          strokeWidth="2"
        />
        <path
          d="M30 25C30 22 32 20 35 20H45C48 20 50 22 50 25V35C50 38 48 40 45 40H35C32 40 30 38 30 35V25Z"
          fill="#ef4444"
        />
        <circle cx="40" cy="30" r="3" fill="#fca5a5" />
        <path
          d="M25 50L20 55C18 57 18 60 20 62L25 67C27 69 30 69 32 67L37 62C39 60 39 57 37 55L32 50"
          fill="#dc2626"
          stroke="#991b1b"
          strokeWidth="2"
        />
      </svg>
    ),
    bg: (
      <div className="pointer-events-none absolute left-1/2 top-1/2 size-1/2 -translate-x-1/2 -translate-y-1/2 overflow-visible rounded-full bg-gradient-to-r from-red-600 via-red-500 to-red-700 opacity-70"></div>
    ),
  },
  {
    icon: (
      <svg
        viewBox="0 0 100 100"
        className="size-full"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M25 30C25 25 30 20 35 20H45C50 20 55 25 55 30V50C55 55 50 60 45 60H35C30 60 25 55 25 50V30Z"
          fill="#ea580c"
          stroke="#c2410c"
          strokeWidth="2"
        />
        <path
          d="M30 25C30 22 32 20 35 20H45C48 20 50 22 50 25V35C50 38 48 40 45 40H35C32 40 30 38 30 35V25Z"
          fill="#fb923c"
        />
        <circle cx="40" cy="30" r="3" fill="#fed7aa" />
        <path
          d="M25 50L20 55C18 57 18 60 20 62L25 67C27 69 30 69 32 67L37 62C39 60 39 57 37 55L32 50"
          fill="#ea580c"
          stroke="#c2410c"
          strokeWidth="2"
        />
      </svg>
    ),
    bg: (
      <div className="pointer-events-none absolute left-1/2 top-1/2 size-1/2 -translate-x-1/2 -translate-y-1/2 overflow-visible rounded-full bg-gradient-to-r from-orange-600 via-orange-500 to-orange-700 opacity-70"></div>
    ),
  },
  {
    icon: (
      <svg
        viewBox="0 0 100 100"
        className="size-full"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M25 30C25 25 30 20 35 20H45C50 20 55 25 55 30V50C55 55 50 60 45 60H35C30 60 25 55 25 50V30Z"
          fill="#1f2937"
          stroke="#111827"
          strokeWidth="2"
        />
        <path
          d="M30 25C30 22 32 20 35 20H45C48 20 50 22 50 25V35C50 38 48 40 45 40H35C32 40 30 38 30 35V25Z"
          fill="#374151"
        />
        <circle cx="40" cy="30" r="3" fill="#6b7280" />
        <path
          d="M25 50L20 55C18 57 18 60 20 62L25 67C27 69 30 69 32 67L37 62C39 60 39 57 37 55L32 50"
          fill="#1f2937"
          stroke="#111827"
          strokeWidth="2"
        />
      </svg>
    ),
    bg: (
      <div className="pointer-events-none absolute left-1/2 top-1/2 size-1/2 -translate-x-1/2 -translate-y-1/2 overflow-visible rounded-full bg-gradient-to-r from-gray-700 via-gray-600 to-gray-800 opacity-70"></div>
    ),
  },
  {
    icon: (
      <svg
        viewBox="0 0 100 100"
        className="size-full"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M25 30C25 25 30 20 35 20H45C50 20 55 25 55 30V50C55 55 50 60 45 60H35C30 60 25 55 25 50V30Z"
          fill="#1d4ed8"
          stroke="#1e40af"
          strokeWidth="2"
        />
        <path
          d="M30 25C30 22 32 20 35 20H45C48 20 50 22 50 25V35C50 38 48 40 45 40H35C32 40 30 38 30 35V25Z"
          fill="#3b82f6"
        />
        <circle cx="40" cy="30" r="3" fill="#93c5fd" />
        <path
          d="M25 50L20 55C18 57 18 60 20 62L25 67C27 69 30 69 32 67L37 62C39 60 39 57 37 55L32 50"
          fill="#1d4ed8"
          stroke="#1e40af"
          strokeWidth="2"
        />
      </svg>
    ),
    bg: (
      <div className="pointer-events-none absolute left-1/2 top-1/2 size-1/2 -translate-x-1/2 -translate-y-1/2 overflow-visible rounded-full bg-gradient-to-r from-blue-600 via-blue-500 to-blue-700 opacity-70"></div>
    ),
  },
  {
    icon: (
      <svg
        viewBox="0 0 100 100"
        className="size-full"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M25 30C25 25 30 20 35 20H45C50 20 55 25 55 30V50C55 55 50 60 45 60H35C30 60 25 55 25 50V30Z"
          fill="#059669"
          stroke="#047857"
          strokeWidth="2"
        />
        <path
          d="M30 25C30 22 32 20 35 20H45C48 20 50 22 50 25V35C50 38 48 40 45 40H35C32 40 30 38 30 35V25Z"
          fill="#10b981"
        />
        <circle cx="40" cy="30" r="3" fill="#6ee7b7" />
        <path
          d="M25 50L20 55C18 57 18 60 20 62L25 67C27 69 30 69 32 67L37 62C39 60 39 57 37 55L32 50"
          fill="#059669"
          stroke="#047857"
          strokeWidth="2"
        />
      </svg>
    ),
    bg: (
      <div className="pointer-events-none absolute left-1/2 top-1/2 size-1/2 -translate-x-1/2 -translate-y-1/2 overflow-visible rounded-full bg-gradient-to-r from-emerald-600 via-emerald-500 to-emerald-700 opacity-70"></div>
    ),
  },
];

function shuffleArray<T>(array: T[]): T[] {
  let currentIndex = array.length;
  let randomIndex;
  while (currentIndex !== 0) {
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;
    [array[currentIndex], array[randomIndex]] = [
      array[randomIndex],
      array[currentIndex],
    ];
  }
  return array;
}

function GloveCard(glove: { icon: JSX.Element; bg: JSX.Element }) {
  const id = useId();
  const controls = useAnimation();
  const ref = useRef(null);
  const inView = useInView(ref, { once: true });

  useEffect(() => {
    if (inView) {
      controls.start({
        opacity: 1,
        transition: { delay: Math.random() * 2, ease: "easeOut", duration: 1 },
      });
    }
  }, [controls, inView]);

  return (
    <motion.div
      key={id}
      ref={ref}
      initial={{ opacity: 0 }}
      animate={controls}
      className={cn(
        "relative size-20 cursor-pointer overflow-hidden rounded-2xl border p-4",
        "bg-white [box-shadow:0_0_0_1px_rgba(0,0,0,.03),0_2px_4px_rgba(0,0,0,.05),0_12px_24px_rgba(0,0,0,.05)]",
        "transform-gpu dark:bg-transparent dark:[border:1px_solid_rgba(255,255,255,.1)] dark:[box-shadow:0_-20px_80px_-20px_#ffffff1f_inset]"
      )}
    >
      {glove.icon}
      {glove.bg}
    </motion.div>
  );
}

export function BoxingGlovesMarquee() {
  const [randomGloves1, setRandomGloves1] = useState<typeof boxingGloves>([]);
  const [randomGloves2, setRandomGloves2] = useState<typeof boxingGloves>([]);
  const [randomGloves3, setRandomGloves3] = useState<typeof boxingGloves>([]);
  const [randomGloves4, setRandomGloves4] = useState<typeof boxingGloves>([]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      setRandomGloves1(shuffleArray([...boxingGloves]));
      setRandomGloves2(shuffleArray([...boxingGloves]));
      setRandomGloves3(shuffleArray([...boxingGloves]));
      setRandomGloves4(shuffleArray([...boxingGloves]));
    }
  }, []);

  return (
    <section id="footer-marquee" className="bg-black py-12">
      <div className="container mx-auto px-4 py-12 md:px-8">
        <div className="flex w-full flex-col items-center justify-center">
          <div className="relative flex w-full flex-col items-center justify-center overflow-hidden">
            <Marquee
              reverse
              className="-delay-[200ms] [--duration:10s]"
              repeat={5}
            >
              {randomGloves1.map((glove, idx) => (
                <GloveCard key={idx} {...glove} />
              ))}
            </Marquee>
            <Marquee reverse className="[--duration:25s]" repeat={5}>
              {randomGloves2.map((glove, idx) => (
                <GloveCard key={idx} {...glove} />
              ))}
            </Marquee>
            <Marquee
              reverse
              className="-delay-[200ms] [--duration:20s]"
              repeat={5}
            >
              {randomGloves3.map((glove, idx) => (
                <GloveCard key={idx} {...glove} />
              ))}
            </Marquee>
            <Marquee reverse className="[--duration:30s]" repeat={5}>
              {randomGloves4.map((glove, idx) => (
                <GloveCard key={idx} {...glove} />
              ))}
            </Marquee>
            <div className="absolute">
              <div className="bg-background absolute inset-0 -z-10 rounded-full opacity-40 blur-xl dark:bg-background" />
            </div>
            <div className="to-background absolute inset-x-0 bottom-0 h-full bg-gradient-to-b from-transparent to-70% dark:to-background" />
          </div>
        </div>
      </div>
    </section>
  );
}
