"use client";

import React, { useState, useEffect } from "react";
import ContactModal from "./ContactModal";
import { LanguageSwitcher } from "./LanguageSwitcher";

interface NavigationProps {
  logoText?: string;
  ctaText?: string;
  ctaHref?: string;
}

const Navigation: React.FC<NavigationProps> = ({
  logoText = "Ivan",
  ctaText = "Train Now",
  ctaHref = "#contact",
}) => {
  const [activeSection, setActiveSection] = useState("hero");

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    const handleScroll = () => {
      const sections = ["hero", "gallery", "about", "comments", "price"];
      const scrollPosition = window.scrollY + 100;

      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const offsetTop = element.offsetTop;
          const offsetHeight = element.offsetHeight;

          if (
            scrollPosition >= offsetTop &&
            scrollPosition < offsetTop + offsetHeight
          ) {
            setActiveSection(section);
            break;
          }
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Call once to set initial state

    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const getButtonClasses = (sectionId: string) => {
    const baseClasses =
      "relative flex items-center justify-center px-4 py-2 text-lg cursor-pointer font-medium outline-none transition focus-visible:outline-2 rounded-full";
    const activeClasses = "bg-orange-600/40";
    const inactiveClasses = "hover:bg-orange-600/20";

    return `${baseClasses} ${
      activeSection === sectionId ? activeClasses : inactiveClasses
    }`;
  };

  return (
    <div className="relative flex mx-auto flex-col">
      {/* Logo */}
      <div className="top-12 absolute left-12 md:left-24 z-[9999]">
        <div className="flex items-center space-x-4">
          <h2 className="text-4xl font-bold text-black">
            <span>{logoText}</span>
          </h2>
          <LanguageSwitcher className="bg-orange-600/20 backdrop-blur-sm rounded-full" />
        </div>
      </div>

      {/* CTA Button - Orange Theme */}
      <div className="top-12 absolute right-12 md:right-24">
        <div className="h-12">
          <ContactModal>
            <button className="inline-block group focus:outline-none">
              <div className="relative flex">
                <div className="z-10 flex">
                  <div className="h-full min-h-[40px] max-h-[40px] flex">
                    <div className="bg-orange-600 text-white h-full flex items-center px-6 rounded-l-full hover:bg-orange-700 transition-colors">
                      <span className="text-lg font-semibold">{ctaText}</span>
                    </div>
                    <div className="bg-orange-600 text-white h-full min-h-[40px] max-h-[40px] flex items-center px-2 rounded-r-full hover:bg-orange-700 transition-colors">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="group-hover:rotate-45 transition-all duration-100 h-4 w-4"
                      >
                        <path d="M7 7h10v10"></path>
                        <path d="M7 17 17 7"></path>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </button>
          </ContactModal>
        </div>
      </div>

      <div className="flex-1">
        <div className="relative z-[9999] w-screen">
          <div className="flex items-center justify-center">
            <div className="fixed bottom-10 md:top-10 z-[9999] h-[50px]">
              <div className="max-w-4xl">
                <div className="flex space-x-4 sticky top-0 z-50 bg-orange-900/60 backdrop-blur-md px-1 py-[3px] rounded-full border border-orange-800/50">
                  <ul className="flex w-full justify-between">
                    <button
                      onClick={() => scrollToSection("hero")}
                      className={getButtonClasses("hero")}
                    >
                      <div className="z-20 flex items-center">
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          className={`h-6 w-6 ${
                            activeSection === "hero"
                              ? "text-orange-200"
                              : "text-orange-100/80"
                          }`}
                        >
                          <path
                            d="M8 17H16M11.0177 2.764L4.23539 8.03912C3.78202 8.39175 3.55534 8.56806 3.39203 8.78886C3.24737 8.98444 3.1396 9.20478 3.07403 9.43905C3 9.70352 3 9.9907 3 10.5651V17.8C3 18.9201 3 19.4801 3.21799 19.908C3.40973 20.2843 3.71569 20.5903 4.09202 20.782C4.51984 21 5.07989 21 6.2 21H17.8C18.9201 21 19.4802 21 19.908 20.782C20.2843 20.5903 20.5903 20.2843 20.782 19.908C21 19.4801 21 18.9201 21 17.8V10.5651C21 9.9907 21 9.70352 20.926 9.43905C20.8604 9.20478 20.7526 8.98444 20.608 8.78886C20.4447 8.56806 20.218 8.39175 19.7646 8.03913L12.9823 2.764C12.631 2.49075 12.4553 2.35412 12.2613 2.3016C12.0902 2.25526 11.9098 2.25526 11.7387 2.3016C11.5447 2.35412 11.369 2.49075 11.0177 2.764Z"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                          />
                        </svg>
                      </div>
                    </button>
                    <button
                      onClick={() => scrollToSection("gallery")}
                      className={getButtonClasses("gallery")}
                    >
                      <div className="z-20 flex items-center">
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          className={`h-6 w-6 ${
                            activeSection === "gallery"
                              ? "text-orange-200"
                              : "text-orange-100/80"
                          }`}
                        >
                          <path
                            d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </button>
                    <button
                      onClick={() => scrollToSection("about")}
                      className={getButtonClasses("about")}
                    >
                      <div className="z-20 flex items-center">
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          className={`h-6 w-6 ${
                            activeSection === "about"
                              ? "text-orange-200"
                              : "text-orange-100/80"
                          }`}
                        >
                          <circle
                            cx="12"
                            cy="12"
                            r="3"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                          <path
                            d="M12 1v6m0 6v6m11-7h-6m-6 0H1"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                        </svg>
                      </div>
                    </button>
                    <button
                      onClick={() => scrollToSection("comments")}
                      className={getButtonClasses("comments")}
                    >
                      <div className="z-20 flex items-center">
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          className={`h-6 w-6 ${
                            activeSection === "comments"
                              ? "text-orange-200"
                              : "text-orange-100/80"
                          }`}
                        >
                          <path
                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </button>
                    <button
                      onClick={() => scrollToSection("price")}
                      className={getButtonClasses("price")}
                    >
                      <div className="z-20 flex items-center">
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          className={`h-6 w-6 ${
                            activeSection === "price"
                              ? "text-orange-200"
                              : "text-orange-100/80"
                          }`}
                        >
                          <path
                            d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </button>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Navigation;
