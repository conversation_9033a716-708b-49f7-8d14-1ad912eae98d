"use client";

import React from "react";
import { useIntl } from "react-intl";

interface HeroSectionProps {
  videoSrc?: string;
}

export const HeroSection = ({ videoSrc }: HeroSectionProps) => {
  const intl = useIntl();
  return (
    <section id="hero">
      <div className="h-full md:h-[900px] relative">
        {videoSrc && (
          <div className="absolute inset-0 w-full h-full overflow-hidden">
            <video
              autoPlay
              muted
              loop
              playsInline
              className="absolute inset-0 w-full h-full object-cover"
            >
              <source src={videoSrc} type="video/mp4" />
            </video>
            <div className="absolute inset-0 bg-black/30"></div>
          </div>
        )}

        <div className="relative z-10">
          <div className="h-full w-full relative md:z-0">
            {/* Hero Background Gradient - Orange Theme */}
            <div className="hidden md:block absolute md:top-24 right-0">
              <div style={{ opacity: 1, transform: "none" }}>
                <div className="w-[600px] h-[600px] bg-gradient-to-br from-orange-500 to-orange-700 rounded-full opacity-20 animate-pulse"></div>
              </div>
            </div>

            {/* Main Hero Text */}
            <div className="absolute top-[calc(100vh-260px)] md:top-56 z-[9999] md:left-16 w-full md:w-auto pr-4">
              <div style={{ opacity: 1, transform: "none" }}>
                <span className="flex items-end justify-end md:justify-start flex-col text-right md:text-left tracking-tight pb-3 bg-clip-text font-sans text-transparent bg-gradient-to-t from-neutral-800 to-neutral-950 dark:from-stone-200 dark:to-neutral-200 text-5xl sm:text-6xl lg:text-[6rem] font-bold">
                  <h1 className="md:hidden font-black text-4xl sm:text-5xl text-white leading-tight">
                    {intl
                      .formatMessage({ id: "hero.title" })
                      .split(" ")
                      .map((word: string, idx: number) => (
                        <React.Fragment key={idx}>
                          {word}
                          <br />
                        </React.Fragment>
                      ))}
                  </h1>
                  <h1 className="hidden md:flex items-center font-semibold flex-col text-[12.5rem] pb-4 text-white">
                    {intl
                      .formatMessage({ id: "hero.title" })
                      .split(" ")
                      .map((word: string, idx: number) => (
                        <React.Fragment key={idx}>
                          {word}
                          <br />
                        </React.Fragment>
                      ))}
                  </h1>
                </span>
              </div>
            </div>

            {/* Coach Name Display - Orange Theme */}
            <div className="absolute top-[calc(100vh-530px)] md:top-[calc(100vh-530px)] right-7">
              <h2 className="font-bold pt-9 text-6xl md:text-[12.5rem] text-orange-600">
                <span
                  className="inline-block mr-[0.25em] whitespace-nowrap"
                  aria-hidden="true"
                >
                  {intl
                    .formatMessage({ id: "hero.coachName" })
                    .split("")
                    .map((char: string, idx: number) => (
                      <span
                        key={idx}
                        aria-hidden="true"
                        className="inline-block -mr-[0.01em]"
                        style={{
                          opacity: 1,
                          transform: "translateY(0em) translateZ(0px)",
                        }}
                      >
                        {char}
                      </span>
                    ))}
                </span>
              </h2>
            </div>
          </div>

          {/* Mobile Hero Image - Orange Theme */}
          <div className="md:hidden">
            <div
              style={{
                opacity: 0,
                transform: "translateY(24px) translateZ(0)",
              }}
            >
              <div className="relative h-screen max-h-[1000px] w-full min-h-[500px] lg:min-h-[600px] before:absolute before:inset-0 before:bg-orange-600 before:opacity-30 overflow-hidden">
                <div className="absolute inset-0 h-full w-full bg-gradient-to-br from-orange-500 to-orange-700 rounded-br-[88px]"></div>
              </div>
            </div>
          </div>

          {/* Buttons */}
          <div className="absolute bottom-10 left-0 w-full flex justify-center gap-4 z-50">
            <button className="px-6 py-3 bg-orange-600 text-white rounded font-bold shadow">
              {intl.formatMessage({ id: "hero.cta" })}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};
