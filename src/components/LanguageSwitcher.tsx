"use client";

import React from 'react';
import { useIntl } from '@/i18n/IntlProvider';
import { Globe } from 'lucide-react';

interface LanguageSwitcherProps {
  className?: string;
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ 
  className = "" 
}) => {
  const { locale, toggleLocale } = useIntl();

  return (
    <button
      onClick={toggleLocale}
      className={`inline-flex items-center px-3 py-2 text-sm font-medium text-orange-100 hover:text-orange-200 transition-colors ${className}`}
      aria-label="Switch language"
    >
      <Globe className="w-4 h-4 mr-2" />
      <span className="uppercase font-semibold">
        {locale === 'en' ? 'EN' : 'UA'}
      </span>
    </button>
  );
};
