import React from "react";
import { useIntl } from "react-intl";

interface PricingPlan {
  name: string;
  duration: string;
  price: string;
  description: string;
  features: string[];
  gradient: string;
  textColor: string;
  buttonBgColor?: string;
}

interface PricingSectionProps {
  title?: string;
  subtitle?: string;
  plans?: PricingPlan[];
}

// This will be replaced by internationalized content
const defaultPlans: PricingPlan[] = [];

const PricingCard: React.FC<{ plan: PricingPlan }> = ({ plan }) => {
  return (
    <div
      className={`relative shadow-sm h-auto min-h-[600px] w-full md:w-[400px] rounded-[28px] border border-black/5 ${plan.textColor} p-8`}
      style={{ backgroundImage: plan.gradient }}
    >
      <div className="flex flex-col h-full justify-between">
        <div className="flex flex-col space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-black leading-[1.2353641176] tracking-wide">
              {plan.name}
            </h3>
            <span className="text-sm font-semibold opacity-80">
              {plan.duration}
            </span>
          </div>
          <div className="mt-2 flex items-baseline justify-start gap-x-2">
            <span className="text-4xl font-bold tracking-tight">
              {plan.price}
            </span>
          </div>
          <p
            className={`text-base font-semibold leading-[1.4] tracking-wide ${plan.textColor} opacity-90`}
          >
            {plan.description}
          </p>

          {/* Features list */}
          <div className="mt-6 space-y-3">
            {plan.features.map((feature, index) => (
              <div
                key={`${feature.slice(0, 20)}-${index}`}
                className="flex items-start space-x-3"
              >
                <div className="flex-shrink-0 w-2 h-2 bg-orange-400 rounded-full mt-2"></div>
                <p
                  className={`text-sm leading-[1.5] ${plan.textColor} opacity-85`}
                >
                  {feature}
                </p>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-8">
          <button
            className={`${
              plan.buttonBgColor || "bg-orange-600"
            } w-full py-4 px-6 rounded-xl text-white font-semibold hover:opacity-90 transition-opacity`}
          >
            Get Started
          </button>
        </div>
      </div>
    </div>
  );
};

const PricingSection: React.FC<PricingSectionProps> = ({
  title,
  subtitle,
  plans,
}) => {
  const intl = useIntl();

  // Create plans from internationalization messages
  const pricingPlans: PricingPlan[] = plans || [
    {
      name: intl.formatMessage({ id: "pricing.starterFocus.name" }),
      duration: intl.formatMessage({ id: "pricing.starterFocus.duration" }),
      price: intl.formatMessage({ id: "pricing.starterFocus.price" }),
      description: intl.formatMessage({
        id: "pricing.starterFocus.description",
      }),
      features: [
        intl.formatMessage({ id: "pricing.starterFocus.feature1" }),
        intl.formatMessage({ id: "pricing.starterFocus.feature2" }),
        intl.formatMessage({ id: "pricing.starterFocus.feature3" }),
        intl.formatMessage({ id: "pricing.starterFocus.feature4" }),
        intl.formatMessage({ id: "pricing.starterFocus.feature5" }),
      ],
      gradient:
        "linear-gradient(135deg, #1c1c1c 0%, #242424 50%, #0a0a0a 100%)",
      textColor: "text-white",
      buttonBgColor: "bg-orange-600",
    },
    {
      name: intl.formatMessage({ id: "pricing.deepWork.name" }),
      duration: intl.formatMessage({ id: "pricing.deepWork.duration" }),
      price: intl.formatMessage({ id: "pricing.deepWork.price" }),
      description: intl.formatMessage({ id: "pricing.deepWork.description" }),
      features: [
        intl.formatMessage({ id: "pricing.deepWork.feature1" }),
        intl.formatMessage({ id: "pricing.deepWork.feature2" }),
        intl.formatMessage({ id: "pricing.deepWork.feature3" }),
        intl.formatMessage({ id: "pricing.deepWork.feature4" }),
        intl.formatMessage({ id: "pricing.deepWork.feature5" }),
        intl.formatMessage({ id: "pricing.deepWork.feature6" }),
      ],
      gradient:
        "linear-gradient(135deg, #ffd479 0%, #ff5a00 50%, #c24914 100%)",
      textColor: "text-black",
    },
  ];

  const sectionTitle = title || intl.formatMessage({ id: "pricing.title" });
  const sectionSubtitle =
    subtitle || intl.formatMessage({ id: "pricing.subtitle" });
  return (
    <div className="relative h-full bg-black rounded-t-[4rem]">
      <section id="price">
        <div className="w-full h-full md:h-[900px]">
          <div className="flex py-9 justify-center items-center flex-col">
            <div>
              <h2
                style={{ display: "flex", overflow: "hidden" }}
                className="text-[2.3rem] font-bold md:text-[6rem] md:font-medium tracking-tighter text-orange-50"
              >
                {sectionTitle.split("").map((char, index) => (
                  <span
                    key={`${char}-${index}`}
                    style={{ transform: "translateY(0%) translateZ(0px)" }}
                  >
                    {char === " " ? "\u00A0" : char}
                  </span>
                ))}
              </h2>
            </div>
            <h3>
              <span className="tracking-tight pb-3 bg-clip-text font-sans text-transparent bg-gradient-to-t from-neutral-200 to-neutral-300 text-xl sm:text-2xl lg:text-3xl font-bold">
                {sectionSubtitle}
              </span>
            </h3>
          </div>
          <div className="flex flex-col md:flex-row items-center justify-center w-full gap-6 px-2">
            {pricingPlans.map((plan, index) => (
              <PricingCard key={`${plan.name}-${index}`} plan={plan} />
            ))}
          </div>

          {/* Additional pricing notes */}
          <div className="mt-12 px-6 text-center">
            <div className="max-w-4xl mx-auto space-y-2 text-sm text-orange-100/70">
              <p>• {intl.formatMessage({ id: "pricing.singleSession" })}</p>
              <p>• {intl.formatMessage({ id: "pricing.corporateWorkshop" })}</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PricingSection;
