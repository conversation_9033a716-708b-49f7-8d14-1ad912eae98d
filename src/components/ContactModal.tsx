"use client";

import React from "react";
import { useIntl } from "react-intl";
import { Phone, MessageCircle, Mail, MapPin } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface ContactModalProps {
  children: React.ReactNode;
}

const ContactModal: React.FC<ContactModalProps> = ({ children }) => {
  const intl = useIntl();

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center text-orange-600">
            {intl.formatMessage({ id: "contact.contactIvan" })}
          </DialogTitle>
          <DialogDescription className="text-center">
            {intl.formatMessage({ id: "contact.description" })}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Phone Contact */}
          <a
            href="tel:+380123456789"
            className="flex items-center gap-4 p-4 rounded-lg border border-orange-200 hover:border-orange-400 hover:bg-orange-50 transition-all duration-200 group"
          >
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-orange-100 group-hover:bg-orange-200 transition-colors">
              <Phone className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">
                {intl.formatMessage({ id: "contact.callIvan" })}
              </h3>
              <p className="text-sm text-gray-600">+380 123 456 789</p>
            </div>
          </a>

          {/* Telegram Contact */}
          <a
            href="https://t.me/ivan_boxing_coach"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-4 p-4 rounded-lg border border-blue-200 hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 group"
          >
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 group-hover:bg-blue-200 transition-colors">
              <MessageCircle className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">
                {intl.formatMessage({ id: "contact.telegram" })}
              </h3>
              <p className="text-sm text-gray-600">@ivan_boxing_coach</p>
            </div>
          </a>

          {/* Email Contact */}
          <a
            href="mailto:<EMAIL>"
            className="flex items-center gap-4 p-4 rounded-lg border border-green-200 hover:border-green-400 hover:bg-green-50 transition-all duration-200 group"
          >
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-green-100 group-hover:bg-green-200 transition-colors">
              <Mail className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">
                {intl.formatMessage({ id: "contact.email" })}
              </h3>
              <p className="text-sm text-gray-600"><EMAIL></p>
            </div>
          </a>

          {/* Location */}
          <div className="flex items-center gap-4 p-4 rounded-lg border border-gray-200 bg-gray-50">
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-100">
              <MapPin className="w-6 h-6 text-gray-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">
                {intl.formatMessage({ id: "contact.trainingLocation" })}
              </h3>
              <p className="text-sm text-gray-600">
                {intl.formatMessage({ id: "contact.location" })}
              </p>
            </div>
          </div>
        </div>

        <div className="text-center pt-4 border-t">
          <p className="text-sm text-gray-500">
            {intl.formatMessage({ id: "contact.experience" })}
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ContactModal;
