"use client";

import React, { useState } from "react";
import { useIntl } from "react-intl";

interface FAQItem {
  question: string;
  answer: string;
}

interface FAQSectionProps {
  title?: string;
  subtitle?: string;
  faqs?: FAQItem[];
}

// This will be replaced by internationalized content
const defaultFAQs: FAQItem[] = [];

const FAQItem: React.FC<{
  faq: FAQItem;
  isOpen: boolean;
  onToggle: () => void;
}> = ({ faq, isOpen, onToggle }) => {
  return (
    <div
      className="border-x border-b-0 border-black/10 rounded-md md:px-4"
      data-state={isOpen ? "open" : "closed"}
    >
      <h3 className="flex">
        <button
          type="button"
          onClick={onToggle}
          aria-expanded={isOpen}
          className="flex flex-1 items-center justify-between py-4 transition-all text-xl md:text-3xl text-left pr-4 md:pr-0 font-medium"
        >
          <span className="px-6 md:px-2">{faq.question}</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className={`lucide lucide-arrow-down-left-square h-12 w-12 shrink-0 transition-transform duration-200 fill-orange-200 stroke-black ${
              isOpen ? "rotate-180" : ""
            }`}
          >
            <rect width="18" height="18" x="3" y="3" rx="2" />
            <path d="m16 8-8 8" />
            <path d="M16 16H8V8" />
          </svg>
        </button>
      </h3>
      <div
        className={`overflow-hidden text-sm transition-all duration-300 ease-in-out ${
          isOpen ? "max-h-96 opacity-100 pb-4" : "max-h-0 opacity-0"
        }`}
      >
        <div className="px-6 md:px-2 text-lg text-neutral-200">
          {faq.answer}
        </div>
      </div>
    </div>
  );
};

const FAQSection: React.FC<FAQSectionProps> = ({ title, subtitle, faqs }) => {
  const intl = useIntl();
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  // Create FAQs from internationalization messages
  const faqItems: FAQItem[] = faqs || [
    {
      question: intl.formatMessage({ id: "faq.q1.question" }),
      answer: intl.formatMessage({ id: "faq.q1.answer" }),
    },
    {
      question: intl.formatMessage({ id: "faq.q2.question" }),
      answer: intl.formatMessage({ id: "faq.q2.answer" }),
    },
    {
      question: intl.formatMessage({ id: "faq.q3.question" }),
      answer: intl.formatMessage({ id: "faq.q3.answer" }),
    },
    {
      question: intl.formatMessage({ id: "faq.q4.question" }),
      answer: intl.formatMessage({ id: "faq.q4.answer" }),
    },
    {
      question: intl.formatMessage({ id: "faq.q5.question" }),
      answer: intl.formatMessage({ id: "faq.q5.answer" }),
    },
    {
      question: intl.formatMessage({ id: "faq.q6.question" }),
      answer: intl.formatMessage({ id: "faq.q6.answer" }),
    },
    {
      question: intl.formatMessage({ id: "faq.q7.question" }),
      answer: intl.formatMessage({ id: "faq.q7.answer" }),
    },
    {
      question: intl.formatMessage({ id: "faq.q8.question" }),
      answer: intl.formatMessage({ id: "faq.q8.answer" }),
    },
    {
      question: intl.formatMessage({ id: "faq.q9.question" }),
      answer: intl.formatMessage({ id: "faq.q9.answer" }),
    },
    {
      question: intl.formatMessage({ id: "faq.q10.question" }),
      answer: intl.formatMessage({ id: "faq.q10.answer" }),
    },
    {
      question: intl.formatMessage({ id: "faq.q11.question" }),
      answer: intl.formatMessage({ id: "faq.q11.answer" }),
    },
    {
      question: intl.formatMessage({ id: "faq.q12.question" }),
      answer: intl.formatMessage({ id: "faq.q12.answer" }),
    },
  ];

  const sectionTitle = title || intl.formatMessage({ id: "faq.title" });

  const handleToggle = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="relative">
      <div className="w-full h-full">
        <div className="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8 lg:py-20 bg-transparent rounded-t-[48px]">
          <div className="py-12">
            <div>
              <h2
                style={{ display: "flex", overflow: "hidden" }}
                className="md:text-[6rem] text-[2rem] font-bold md:leading-10 md:pb-14 tracking-tight text-orange-100 md:py-8"
              >
                {sectionTitle.split("").map((char, index) => (
                  <span
                    key={`${char}-${index}`}
                    style={{ transform: "translateY(0%) translateZ(0px)" }}
                  >
                    {char === " " ? "\u00A0" : char}
                  </span>
                ))}
              </h2>
            </div>
          </div>
          <div className="absolute inset-0 -z-10 h-full w-full items-center px-5 py-24 [background:radial-gradient(125%_125%_at_50%_10%,#000_50%,#FF7C33_100%)]" />
          <div className="md:mx-auto">
            <div
              className="w-full md:space-y-9 bg-black/10 rounded-xl border border-orange-50/20 text-white backdrop-blur"
              data-orientation="vertical"
            >
              {faqItems.map((faq, index) => (
                <FAQItem
                  key={`${faq.question.slice(0, 20)}-${index}`}
                  faq={faq}
                  isOpen={openIndex === index}
                  onToggle={() => handleToggle(index)}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
