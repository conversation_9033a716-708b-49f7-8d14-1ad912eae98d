const fs = require('fs');
const path = require('path');

// Directory containing message files
const messagesDir = path.join(__dirname, '../src/i18n/messages');
const outputDir = path.join(__dirname, '../src/i18n/locales');

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Function to collect messages from .message.ts files
function collectMessages() {
  const files = fs.readdirSync(messagesDir);
  
  files.forEach(file => {
    if (file.endsWith('.message.ts')) {
      const locale = file.replace('.message.ts', '');
      const filePath = path.join(messagesDir, file);
      
      try {
        // Read the TypeScript file
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Extract the exported object (simple regex approach)
        const match = content.match(/export const \w+Messages = ({[\s\S]*});/);
        if (match) {
          // Convert to JSON by evaluating the object literal
          const objectString = match[1];
          const messages = eval(`(${objectString})`);
          
          // Write JSON file
          const outputPath = path.join(outputDir, `${locale}.json`);
          fs.writeFileSync(outputPath, JSON.stringify(messages, null, 2));
          
          console.log(`✅ Generated ${locale}.json`);
        } else {
          console.error(`❌ Could not parse messages from ${file}`);
        }
      } catch (error) {
        console.error(`❌ Error processing ${file}:`, error.message);
      }
    }
  });
}

// Run the collection
console.log('🔄 Collecting message files...');
collectMessages();
console.log('✨ Message collection complete!');
